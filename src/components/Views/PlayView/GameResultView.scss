.game-result-modal {
  flex: auto !important;
  flex-direction: row;
  // justify-content: center;
  // align-items: center;
  gap: 10px;
  margin-top: 104px;

  .result-container {
    max-width: 600px;
    width: 100%;
    background: linear-gradient(135deg, #002332 0%, #001a26 100%);
    border: 2px solid #88FFD5;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(136, 255, 213, 0.3);
    animation: resultAppear 0.8s ease-out;

    &.loading {
      border-color: #6b7280;

      .loading-spinner {
        margin: 20px 0;

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(136, 255, 213, 0.3);
          border-top: 4px solid #88FFD5;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        }
      }
    }

    &.error {
      border-color: #ef4444;

      h2 {
        color: #ef4444;
        margin-bottom: 20px;
      }
    }
  }

  .result-films {
    max-width: 471px;
    background: linear-gradient(135deg, #002332 0%, #001a26 100%);
    border: 2px solid #88FFD5;
    border-radius: 20px;
    padding: 30px;
    animation: resultAppear 0.8s ease-out 0.4s both;

    h3 {
      margin: 0 0 20px 0;
      font-size: 24px;
      color: #88FFD5;
      font-weight: 600;
      text-align: center;
    }

    .movistar-loading {
      text-align: center;
      padding: 40px 20px;

      .loading-spinner {
        margin: 20px 0;

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(136, 255, 213, 0.3);
          border-top: 4px solid #88FFD5;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        }
      }

      p {
        color: #a8a8a8;
        font-size: 14px;
        margin: 0;
      }
    }

    .movistar-content {
      .content-card {
        background: rgba(136, 255, 213, 0.05);
        border: 1px solid rgba(136, 255, 213, 0.2);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(136, 255, 213, 0.4);
          box-shadow: 0 4px 20px rgba(136, 255, 213, 0.1);
        }

        .content-image {
          width: 100%;
          height: 200px;
          overflow: hidden;
          background: rgba(136, 255, 213, 0.1);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }

        .content-info {
          padding: 20px;

          .content-title {
            margin: 0 0 12px 0;
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            line-height: 1.3;
          }

          .content-description {
            margin: 0 0 16px 0;
            font-size: 14px;
            color: #a8a8a8;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .content-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;

            span {
              padding: 4px 8px;
              border-radius: 6px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(136, 255, 213, 0.1);
              color: #88FFD5;
              border: 1px solid rgba(136, 255, 213, 0.2);
            }

            .content-available {
              &.available {
                background: rgba(34, 197, 94, 0.1);
                color: #22c55e;
                border-color: rgba(34, 197, 94, 0.2);
              }

              &.unavailable {
                background: rgba(239, 68, 68, 0.1);
                color: #ef4444;
                border-color: rgba(239, 68, 68, 0.2);
              }
            }
          }

          .movistar-branding {
            text-align: center;
            padding-top: 16px;
            border-top: 1px solid rgba(136, 255, 213, 0.1);

            span {
              font-size: 12px;
              color: #6b7280;
              font-style: italic;
            }
          }
        }
      }
    }

    .no-content, .no-character {
      text-align: center;
      padding: 40px 20px;
      color: #a8a8a8;
      font-size: 14px;
      background: rgba(136, 255, 213, 0.05);
      border: 1px solid rgba(136, 255, 213, 0.1);
      border-radius: 12px;
    }
  }
}

.result-emoji {
  font-size: 80px;
  margin-bottom: 20px;
  animation: bounce 0.6s ease-out;
  display: block;
}

.result-title {
  font-size: clamp(32px, 5vw, 48px);
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
  text-shadow: 0 2px 20px rgba(136, 255, 213, 0.5);
  animation: titleSlide 0.8s ease-out 0.2s both;
}

.result-subtitle {
  font-size: clamp(18px, 3vw, 24px);
  color: #a8a8a8;
  margin: 0 0 30px 0;
  opacity: 0.9;
  animation: fadeIn 0.8s ease-out 0.4s both;
}

.result-message {
  background: rgba(136, 255, 213, 0.1);
  border: 1px solid rgba(136, 255, 213, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin: 30px 0;
  animation: fadeIn 0.8s ease-out 0.6s both;

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
    color: #e8e8e8;
  }
}

.game-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin: 30px 0;
  animation: fadeIn 0.8s ease-out 0.8s both;

  @media (min-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 640px) {
    grid-template-columns: repeat(3, 1fr);
  }

  .stat-item {
    background: rgba(136, 255, 213, 0.05);
    border: 1px solid rgba(136, 255, 213, 0.2);
    border-radius: 8px;
    padding: 16px;

    .stat-label {
      display: block;
      font-size: 14px;
      color: #a8a8a8;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .stat-value {
      display: block;
      font-size: 18px;
      font-weight: 600;
      color: #88FFD5;
    }
  }
}

.character-reveal {
  margin: 30px 0;
  padding: 20px;
  background: linear-gradient(135deg, rgba(136, 255, 213, 0.1), rgba(136, 255, 213, 0.05));
  border: 1px solid rgba(136, 255, 213, 0.3);
  border-radius: 16px;
  animation: fadeIn 0.8s ease-out 1s both;

  h3 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #88FFD5;
    font-weight: 600;
  }

  .character-name {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(136, 255, 213, 0.3);
    padding: 12px 20px;
    background: rgba(136, 255, 213, 0.15);
    border-radius: 12px;
    border: 1px solid rgba(136, 255, 213, 0.4);
  }
}

.result-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 40px;
  animation: fadeIn 0.8s ease-out 1.2s both;

  @media (min-width: 480px) {
    flex-direction: row;
    justify-content: center;
  }

  .secondary-button {
    padding: 12px 32px;
    background: transparent;
    border: 2px solid rgba(136, 255, 213, 0.5);
    border-radius: 8px;
    color: #88FFD5;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 160px;

    &:hover {
      background: rgba(136, 255, 213, 0.1);
      border-color: #88FFD5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(136, 255, 213, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// ========== ANIMATIONS ==========
@keyframes resultAppear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

@keyframes titleSlide {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// ========== RESPONSIVE ==========
@media (max-width: 768px) {
  .game-result-modal {
    padding: 16px;

    .result-container {
      padding: 30px 20px;
    }
  }

  .result-emoji {
    font-size: 60px;
  }

  .character-name {
    font-size: 22px !important;
  }

  .game-stats {
    grid-template-columns: 1fr !important;
    gap: 12px;

    .stat-item {
      padding: 12px;
    }
  }
}

@media (max-width: 480px) {
  .result-container {
    border-radius: 16px !important;
    margin: 10px !important;
  }

  .result-actions {
    .secondary-button {
      min-width: 100%;
    }
  }
}
