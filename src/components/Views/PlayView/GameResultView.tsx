import { useEffect, useState } from "react";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { PrimaryButton } from "microapps";
import { movistarAPIService } from "../../../services/MovistarAPIService";
import type { MovistarContent } from "../../../models/services";
import "./GameResultView.scss";

interface GameResult {
  winner: "ai" | "user" | "draw";
  finalGuess?: string;
  wasCorrect?: boolean;
  currentCharacter?: string;
  questionsUsed: number;
  maxQuestions: number;
  mode: "ia_vs_player";
  endTime: Date;
}

interface GameResultViewProps {
  onPlayAgain: () => void;
  onBackToMain: () => void;
}

const GameResultView: React.FC<GameResultViewProps> = ({
  onPlayAgain,
  onBackToMain,
}) => {
  const { session } = useEnygmaGame();
  const [gameResult, setGameResult] = useState<GameResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasSpokenResult, setHasSpokenResult] = useState(false);
  const [movistarContent, setMovistarContent] = useState<MovistarContent | null>(null);
  const [isLoadingMovistar, setIsLoadingMovistar] = useState(false);

  useEffect(() => {
    const loadGameResult = () => {
      try {
        if (session && session.phase === "finished") {
          const result: GameResult = {
            currentCharacter: session.currentCharacter,
            endTime: session.endTime || new Date(),
            finalGuess: session.finalGuess,
            maxQuestions: session.maxQuestions,
            mode: session.mode,
            questionsUsed: session.questionCount,
            wasCorrect: session.wasCorrect,
            winner: session.winner || "draw",
          };
          setGameResult(result);

          // Guardar también en localStorage para persistencia
          localStorage.setItem(
            "enygma_last_game_result",
            JSON.stringify(result)
          );
        } else {
          const savedResult = localStorage.getItem("enygma_last_game_result");
          if (savedResult) {
            const parsed = JSON.parse(savedResult);
            parsed.endTime = new Date(parsed.endTime);
            setGameResult(parsed);
          }
        }
      } catch (error) {
        console.error("Error cargando resultado del juego:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGameResult();
  }, [session]);

  useEffect(() => {
    if (gameResult && !hasSpokenResult) {
      const narrateResult = async () => {
        try {
          setHasSpokenResult(true);
        } catch (error) {
          console.error("Error narrando resultado:", error);
          setHasSpokenResult(true);
        }
      };

      const timer = setTimeout(narrateResult, 1000);
      return () => clearTimeout(timer);
    }
  }, [gameResult, hasSpokenResult]);

  // Buscar contenido de Movistar Plus cuando se carga el resultado
  useEffect(() => {
    const searchMovistarContent = async () => {
      if (gameResult?.currentCharacter && !movistarContent) {
        setIsLoadingMovistar(true);
        try {
          console.log(`🔍 Buscando contenido de Movistar Plus para: ${gameResult.currentCharacter}`);
          const content = await movistarAPIService.searchByCharacter(gameResult.currentCharacter);

          if (content) {
            console.log(`✅ Contenido encontrado:`, content);
            setMovistarContent(content);
          } else {
            console.log(`❌ No se encontró contenido para: ${gameResult.currentCharacter}`);
          }
        } catch (error) {
          console.error('❌ Error buscando contenido de Movistar Plus:', error);
        } finally {
          setIsLoadingMovistar(false);
        }
      }
    };

    searchMovistarContent();
  }, [gameResult, movistarContent]);

  const getResultMessage = (): string => {
    if (!gameResult) return "";

    const { winner, currentCharacter } = gameResult;

    if (winner === "user") {
      return `El personaje es ${currentCharacter}.`;
    } else if (winner === "ai") {
      return `El personaje es ${currentCharacter}, pero no estabas tan lejos... ¿Quieres probar suerte con otro misterio?`;
    } else {
      return "¡Empate!";
    }
  };

  const getResultTitle = (): string => {
    if (!gameResult) return "";

    const { winner } = gameResult;

    if (winner === "user") {
      return "¡Has acertado!";
    } else if (winner === "ai") {
      return "¡Buen intento!";
    } else {
      return "¡Empate!";
    }
  };

  const handlePlayAgain = () => {
    localStorage.removeItem("enygma_last_game_result");
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("clues");
    setHasSpokenResult(false);
    onPlayAgain();
  };

  const handleBackToMain = () => {
    localStorage.removeItem("enygma_last_game_result");
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("clues");
    setHasSpokenResult(false);
    onBackToMain();
  };

  if (isLoading) {
    return (
      <div className="content game-result-modal">
        <div className="result-container loading">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>Analizando los resultados...</p>
        </div>
      </div>
    );
  }

  if (!gameResult) {
    return (
      <div className="content game-result-modal">
        <div className="result-container error">
          <h2>Error</h2>

          <p>No se pudieron cargar los resultados del juego.</p>

          <PrimaryButton
            onClick={handleBackToMain}
            text="Volver al menú"
            backgroundColor="#88FFD5"
            textColor="#001428"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="content game-result-modal">
      <div className="result-container">
        <h1 className="result-title">
          {getResultTitle()}
        </h1>

        {/* Mensaje descriptivo */}
        <div className="result-message">
          <p>{getResultMessage()}</p>
        </div>

        {/* Botones de acción */}
        <div className="result-actions">
          <PrimaryButton
            onClick={handlePlayAgain}
            text="Jugar de nuevo"
            backgroundColor="#88FFD5"
            textColor="#001428"
            borderRadius="8px"
            spinnerColor="#000"
          />

          {/* <button onClick={handleBackToMain} className="secondary-button">
            Volver al menú
          </button> */}
        </div>
      </div>

      <div className="result-films">
        <h3>Contenido relacionado</h3>

        {isLoadingMovistar ? (
          <div className="movistar-loading">
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
            <p>Buscando contenido en Movistar Plus...</p>
          </div>
        ) : movistarContent ? (
          <div className="movistar-content">
            <div className="content-card">
              {movistarContent.image && (
                <div className="content-image">
                  <img
                    src={movistarContent.image}
                    alt={movistarContent.title}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}

              <div className="content-info">
                <h4 className="content-title">{movistarContent.title}</h4>

                {movistarContent.description && (
                  <p className="content-description">{movistarContent.description}</p>
                )}

                <div className="content-details">
                  {movistarContent.type && (
                    <span className="content-type">
                      {movistarContent.type === 'content' ? '📺 Contenido' :
                       movistarContent.type === 'person' ? '👤 Persona' :
                       movistarContent.type === 'character' ? '🎭 Personaje' :
                       movistarContent.type}
                    </span>
                  )}

                  {movistarContent.year && (
                    <span className="content-year">📅 {movistarContent.year}</span>
                  )}

                  {movistarContent.lanzable !== undefined && (
                    <span className={`content-available ${movistarContent.lanzable ? 'available' : 'unavailable'}`}>
                      {movistarContent.lanzable ? '✅ Disponible' : '❌ No disponible'}
                    </span>
                  )}
                </div>

                <div className="movistar-branding">
                  <span>Powered by Movistar Plus</span>
                </div>
              </div>
            </div>
          </div>
        ) : gameResult?.currentCharacter ? (
          <div className="no-content">
            <p>No se encontró contenido relacionado con "{gameResult.currentCharacter}" en Movistar Plus.</p>
          </div>
        ) : (
          <div className="no-character">
            <p>No hay información del personaje para buscar contenido.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameResultView;
